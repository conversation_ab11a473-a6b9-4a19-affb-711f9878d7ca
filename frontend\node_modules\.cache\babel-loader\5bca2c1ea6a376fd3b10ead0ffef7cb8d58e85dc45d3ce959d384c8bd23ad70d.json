{"ast": null, "code": "var _jsxFileName = \"D:\\\\user booking\\\\frontend\\\\src\\\\components\\\\ProtectedRoute.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useUser } from '../context/UserContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ProtectedRoute = ({\n  children\n}) => {\n  _s();\n  const {\n    isAuthenticated,\n    loading\n  } = useUser();\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        fontSize: '18px',\n        color: '#666'\n      },\n      children: \"Loading...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this);\n  }\n  return isAuthenticated ? children : /*#__PURE__*/_jsxDEV(Navigate, {\n    to: \"/login\",\n    replace: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 39\n  }, this);\n};\n_s(ProtectedRoute, \"HQGyjXwqKbtmvR8GvOM2tPU41EA=\", false, function () {\n  return [useUser];\n});\n_c = ProtectedRoute;\nexport default ProtectedRoute;\nvar _c;\n$RefreshReg$(_c, \"ProtectedRoute\");", "map": {"version": 3, "names": ["React", "Navigate", "useUser", "jsxDEV", "_jsxDEV", "ProtectedRoute", "children", "_s", "isAuthenticated", "loading", "style", "display", "justifyContent", "alignItems", "height", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["D:/user booking/frontend/src/components/ProtectedRoute.js"], "sourcesContent": ["import React from 'react';\nimport { Navigate } from 'react-router-dom';\nimport { useUser } from '../context/UserContext';\n\nconst ProtectedRoute = ({ children }) => {\n  const { isAuthenticated, loading } = useUser();\n\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        fontSize: '18px',\n        color: '#666'\n      }}>\n        Loading...\n      </div>\n    );\n  }\n\n  return isAuthenticated ? children : <Navigate to=\"/login\" replace />;\n};\n\nexport default ProtectedRoute;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,OAAO,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEjD,MAAMC,cAAc,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACvC,MAAM;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGP,OAAO,CAAC,CAAC;EAE9C,IAAIO,OAAO,EAAE;IACX,oBACEL,OAAA;MAAKM,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE,MAAM;QAChBC,KAAK,EAAE;MACT,CAAE;MAAAV,QAAA,EAAC;IAEH;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAEV;EAEA,OAAOZ,eAAe,GAAGF,QAAQ,gBAAGF,OAAA,CAACH,QAAQ;IAACoB,EAAE,EAAC,QAAQ;IAACC,OAAO;EAAA;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AACtE,CAAC;AAACb,EAAA,CAnBIF,cAAc;EAAA,QACmBH,OAAO;AAAA;AAAAqB,EAAA,GADxClB,cAAc;AAqBpB,eAAeA,cAAc;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}