{"ast": null, "code": "var _jsxFileName = \"D:\\\\user booking\\\\frontend\\\\src\\\\context\\\\UserContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { getUserProfile } from '../api/userApi';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UserContext = /*#__PURE__*/createContext();\nexport const useUser = () => {\n  _s();\n  const context = useContext(UserContext);\n  if (!context) {\n    throw new Error('useUser must be used within a UserProvider');\n  }\n  return context;\n};\n_s(useUser, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const UserProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      const token = localStorage.getItem('token');\n      const savedUser = localStorage.getItem('user');\n      if (token && savedUser) {\n        try {\n          // Verify token is still valid by fetching user profile\n          const profileData = await getUserProfile();\n          setUser(profileData.user);\n          setIsAuthenticated(true);\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          setUser(null);\n          setIsAuthenticated(false);\n        }\n      }\n      setLoading(false);\n    };\n    checkAuth();\n  }, []);\n  const login = (userData, token) => {\n    localStorage.setItem('token', token);\n    localStorage.setItem('user', JSON.stringify(userData));\n    setUser(userData);\n    setIsAuthenticated(true);\n  };\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n    setIsAuthenticated(false);\n  };\n  const updateUser = userData => {\n    setUser(userData);\n    localStorage.setItem('user', JSON.stringify(userData));\n  };\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser\n  };\n  return /*#__PURE__*/_jsxDEV(UserContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 74,\n    columnNumber: 5\n  }, this);\n};\n_s2(UserProvider, \"xBgiRagNfQVCfEr2dT2PptfN+TE=\");\n_c = UserProvider;\nvar _c;\n$RefreshReg$(_c, \"UserProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "getUserProfile", "jsxDEV", "_jsxDEV", "UserContext", "useUser", "_s", "context", "Error", "UserProvider", "children", "_s2", "user", "setUser", "loading", "setLoading", "isAuthenticated", "setIsAuthenticated", "checkAuth", "token", "localStorage", "getItem", "savedUser", "profileData", "error", "removeItem", "login", "userData", "setItem", "JSON", "stringify", "logout", "updateUser", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/user booking/frontend/src/context/UserContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect } from 'react';\nimport { getUserProfile } from '../api/userApi';\n\nconst UserContext = createContext();\n\nexport const useUser = () => {\n  const context = useContext(UserContext);\n  if (!context) {\n    throw new Error('useUser must be used within a UserProvider');\n  }\n  return context;\n};\n\nexport const UserProvider = ({ children }) => {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [isAuthenticated, setIsAuthenticated] = useState(false);\n\n  // Check if user is logged in on app start\n  useEffect(() => {\n    const checkAuth = async () => {\n      const token = localStorage.getItem('token');\n      const savedUser = localStorage.getItem('user');\n\n      if (token && savedUser) {\n        try {\n          // Verify token is still valid by fetching user profile\n          const profileData = await getUserProfile();\n          setUser(profileData.user);\n          setIsAuthenticated(true);\n        } catch (error) {\n          // Token is invalid, clear storage\n          localStorage.removeItem('token');\n          localStorage.removeItem('user');\n          setUser(null);\n          setIsAuthenticated(false);\n        }\n      }\n      setLoading(false);\n    };\n\n    checkAuth();\n  }, []);\n\n  const login = (userData, token) => {\n    localStorage.setItem('token', token);\n    localStorage.setItem('user', JSON.stringify(userData));\n    setUser(userData);\n    setIsAuthenticated(true);\n  };\n\n  const logout = () => {\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n    setUser(null);\n    setIsAuthenticated(false);\n  };\n\n  const updateUser = (userData) => {\n    setUser(userData);\n    localStorage.setItem('user', JSON.stringify(userData));\n  };\n\n  const value = {\n    user,\n    loading,\n    isAuthenticated,\n    login,\n    logout,\n    updateUser\n  };\n\n  return (\n    <UserContext.Provider value={value}>\n      {children}\n    </UserContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAC7E,SAASC,cAAc,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,WAAW,gBAAGP,aAAa,CAAC,CAAC;AAEnC,OAAO,MAAMQ,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGT,UAAU,CAACM,WAAW,CAAC;EACvC,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,4CAA4C,CAAC;EAC/D;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAQpB,OAAO,MAAMI,YAAY,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EAC5C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGd,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACe,OAAO,EAAEC,UAAU,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkB,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAE9C,IAAIF,KAAK,IAAIG,SAAS,EAAE;QACtB,IAAI;UACF;UACA,MAAMC,WAAW,GAAG,MAAMtB,cAAc,CAAC,CAAC;UAC1CY,OAAO,CAACU,WAAW,CAACX,IAAI,CAAC;UACzBK,kBAAkB,CAAC,IAAI,CAAC;QAC1B,CAAC,CAAC,OAAOO,KAAK,EAAE;UACd;UACAJ,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;UAChCL,YAAY,CAACK,UAAU,CAAC,MAAM,CAAC;UAC/BZ,OAAO,CAAC,IAAI,CAAC;UACbI,kBAAkB,CAAC,KAAK,CAAC;QAC3B;MACF;MACAF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC;IAEDG,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMQ,KAAK,GAAGA,CAACC,QAAQ,EAAER,KAAK,KAAK;IACjCC,YAAY,CAACQ,OAAO,CAAC,OAAO,EAAET,KAAK,CAAC;IACpCC,YAAY,CAACQ,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC,CAAC;IACtDd,OAAO,CAACc,QAAQ,CAAC;IACjBV,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,MAAMc,MAAM,GAAGA,CAAA,KAAM;IACnBX,YAAY,CAACK,UAAU,CAAC,OAAO,CAAC;IAChCL,YAAY,CAACK,UAAU,CAAC,MAAM,CAAC;IAC/BZ,OAAO,CAAC,IAAI,CAAC;IACbI,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;EAED,MAAMe,UAAU,GAAIL,QAAQ,IAAK;IAC/Bd,OAAO,CAACc,QAAQ,CAAC;IACjBP,YAAY,CAACQ,OAAO,CAAC,MAAM,EAAEC,IAAI,CAACC,SAAS,CAACH,QAAQ,CAAC,CAAC;EACxD,CAAC;EAED,MAAMM,KAAK,GAAG;IACZrB,IAAI;IACJE,OAAO;IACPE,eAAe;IACfU,KAAK;IACLK,MAAM;IACNC;EACF,CAAC;EAED,oBACE7B,OAAA,CAACC,WAAW,CAAC8B,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAvB,QAAA,EAChCA;EAAQ;IAAAyB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC3B,GAAA,CAhEWF,YAAY;AAAA8B,EAAA,GAAZ9B,YAAY;AAAA,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}