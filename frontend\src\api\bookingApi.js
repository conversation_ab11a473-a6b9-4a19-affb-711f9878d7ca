import axios from 'axios';
const API = 'http://localhost:2005/api/bookings';

// Set up axios interceptor to include auth token
axios.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const fetchBookings = async () => {
  const res = await axios.get(API);
  return res.data;
};

export const fetchMyBookings = async () => {
  const res = await axios.get(`${API}/my-bookings`);
  return res.data;
};

export const createBooking = async (vehicleType, serviceType, date) => {
  const res = await axios.post(API, { vehicleType, serviceType, date });
  return res.data;
};

export const updateBooking = async (bookingId, status) => {
  const res = await axios.put(`${API}/${bookingId}`, { status });
  return res.data;
};

export const deleteBooking = async (bookingId) => {
  const res = await axios.delete(`${API}/${bookingId}`);
  return res.data;
};
