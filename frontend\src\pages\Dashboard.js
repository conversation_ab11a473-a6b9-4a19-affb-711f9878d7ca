import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../context/UserContext';
import { fetchMyBookings, deleteBooking, updateBooking } from '../api/bookingApi';
import './Dashboard.css';

function Dashboard() {
  const { user, logout } = useUser();
  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    loadBookings();
  }, []);

  const loadBookings = async () => {
    try {
      setLoading(true);
      const data = await fetchMyBookings();
      setBookings(data);
    } catch (err) {
      setError('Failed to load bookings');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteBooking = async (bookingId) => {
    if (window.confirm('Are you sure you want to delete this booking?')) {
      try {
        await deleteBooking(bookingId);
        setBookings(bookings.filter(booking => booking._id !== bookingId));
      } catch (err) {
        setError('Failed to delete booking');
      }
    }
  };

  const handleUpdateStatus = async (bookingId, newStatus) => {
    try {
      await updateBooking(bookingId, newStatus);
      setBookings(bookings.map(booking => 
        booking._id === bookingId 
          ? { ...booking, status: newStatus }
          : booking
      ));
    } catch (err) {
      setError('Failed to update booking status');
    }
  };

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'pending': return '#f39c12';
      case 'confirmed': return '#27ae60';
      case 'completed': return '#2ecc71';
      case 'cancelled': return '#e74c3c';
      default: return '#95a5a6';
    }
  };

  return (
    <div className="dashboard-container">
      <div className="dashboard-header">
        <div className="user-info">
          <h1>Welcome back, {user?.name}!</h1>
          <p>Manage your vehicle service bookings</p>
        </div>
        <div className="header-actions">
          <button 
            className="btn btn-primary"
            onClick={() => navigate('/book')}
          >
            Book New Service
          </button>
          <button 
            className="btn btn-secondary"
            onClick={handleLogout}
          >
            Logout
          </button>
        </div>
      </div>

      <div className="dashboard-content">
        <div className="stats-section">
          <div className="stat-card">
            <h3>{bookings.length}</h3>
            <p>Total Bookings</p>
          </div>
          <div className="stat-card">
            <h3>{bookings.filter(b => b.status === 'Pending').length}</h3>
            <p>Pending</p>
          </div>
          <div className="stat-card">
            <h3>{bookings.filter(b => b.status === 'Completed').length}</h3>
            <p>Completed</p>
          </div>
        </div>

        <div className="bookings-section">
          <h2>Your Bookings</h2>
          
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          {loading ? (
            <div className="loading">Loading your bookings...</div>
          ) : bookings.length === 0 ? (
            <div className="no-bookings">
              <p>You haven't made any bookings yet.</p>
              <button 
                className="btn btn-primary"
                onClick={() => navigate('/book')}
              >
                Book Your First Service
              </button>
            </div>
          ) : (
            <div className="bookings-grid">
              {bookings.map(booking => (
                <div key={booking._id} className="booking-card">
                  <div className="booking-header">
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(booking.status) }}
                    >
                      {booking.status}
                    </span>
                    <div className="booking-actions">
                      <select
                        value={booking.status}
                        onChange={(e) => handleUpdateStatus(booking._id, e.target.value)}
                        className="status-select"
                      >
                        <option value="Pending">Pending</option>
                        <option value="Confirmed">Confirmed</option>
                        <option value="Completed">Completed</option>
                        <option value="Cancelled">Cancelled</option>
                      </select>
                      <button
                        onClick={() => handleDeleteBooking(booking._id)}
                        className="delete-btn"
                        title="Delete booking"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                  
                  <div className="booking-details">
                    <h3>{booking.vehicleType}</h3>
                    <p><strong>Service:</strong> {booking.serviceType}</p>
                    <p><strong>Date:</strong> {formatDate(booking.date)}</p>
                    <p><strong>Booked on:</strong> {formatDate(booking.createdAt)}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

export default Dashboard;
