.dashboard-container {
  min-height: 100vh;
  background: #f8f9fa;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.dashboard-header {
  background: white;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.user-info h1 {
  color: #333;
  font-size: 28px;
  margin: 0 0 5px 0;
  font-weight: 700;
}

.user-info p {
  color: #666;
  margin: 0;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 15px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  display: inline-block;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.dashboard-content {
  padding: 30px;
  max-width: 1200px;
  margin: 0 auto;
}

.stats-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card h3 {
  font-size: 36px;
  font-weight: 700;
  margin: 0 0 10px 0;
  color: #667eea;
}

.stat-card p {
  color: #666;
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.bookings-section h2 {
  color: #333;
  font-size: 24px;
  margin: 0 0 25px 0;
  font-weight: 700;
}

.error-message {
  background: #fdf2f2;
  border: 1px solid #e74c3c;
  border-radius: 8px;
  padding: 15px;
  color: #e74c3c;
  margin-bottom: 20px;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  font-size: 18px;
}

.no-bookings {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.no-bookings p {
  color: #666;
  font-size: 18px;
  margin: 0 0 20px 0;
}

.bookings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.booking-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.booking-card:hover {
  transform: translateY(-5px);
}

.booking-header {
  padding: 20px 20px 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.status-badge {
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.booking-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.status-select {
  padding: 5px 8px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 12px;
  background: white;
}

.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.delete-btn:hover {
  background: #f8f9fa;
}

.booking-details {
  padding: 20px;
}

.booking-details h3 {
  color: #333;
  font-size: 20px;
  margin: 0 0 15px 0;
  font-weight: 600;
}

.booking-details p {
  color: #666;
  margin: 8px 0;
  font-size: 14px;
}

.booking-details strong {
  color: #333;
}

/* Responsive design */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    padding: 20px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .dashboard-content {
    padding: 20px;
  }
  
  .stats-section {
    grid-template-columns: 1fr;
  }
  
  .bookings-grid {
    grid-template-columns: 1fr;
  }
  
  .booking-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .booking-actions {
    width: 100%;
    justify-content: space-between;
  }
}
