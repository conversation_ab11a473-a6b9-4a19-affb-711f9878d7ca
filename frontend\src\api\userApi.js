import axios from 'axios';
const API = 'http://localhost:2005/api/users';

// Set up axios interceptor to include auth token
axios.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export const fetchUsers = async () => {
  const res = await axios.get(API);
  return res.data;
};

export const registerUser = async (name, email, password) => {
  const res = await axios.post(`${API}/register`, { name, email, password });
  return res.data;
};

export const loginUser = async (email, password) => {
  const res = await axios.post(`${API}/login`, { email, password });
  return res.data;
};

export const getUserProfile = async () => {
  const res = await axios.get(`${API}/profile`);
  return res.data;
};
