{"ast": null, "code": "var _jsxFileName = \"D:\\\\user booking\\\\frontend\\\\src\\\\components\\\\Navbar.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useUser } from '../context/UserContext';\nimport './Navbar.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Navbar() {\n  _s();\n  const {\n    user,\n    isAuthenticated,\n    logout\n  } = useUser();\n  const navigate = useNavigate();\n  const handleLogout = () => {\n    logout();\n    navigate('/');\n  };\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navbar\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-brand\",\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: isAuthenticated ? \"/dashboard\" : \"/\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"brand-name\",\n          children: \"AutoCare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"navbar-menu\",\n      children: isAuthenticated ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/dashboard\",\n          className: \"nav-link\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/book\",\n          className: \"nav-link\",\n          children: \"Book Service\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/bookings\",\n          className: \"nav-link\",\n          children: \"My Bookings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"user-menu\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"user-name\",\n            children: [\"Hi, \", user === null || user === void 0 ? void 0 : user.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: handleLogout,\n            className: \"logout-btn\",\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Link, {\n          to: \"/\",\n          className: \"nav-link\",\n          children: \"Home\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/login\",\n          className: \"nav-link\",\n          children: \"Login\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 39,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/register\",\n          className: \"nav-link register-link\",\n          children: \"Register\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n}\n_s(Navbar, \"zEyeNMoRxaSV9pqrRhnpYHuOsqo=\", false, function () {\n  return [useUser, useNavigate];\n});\n_c = Navbar;\nexport default Navbar;\nvar _c;\n$RefreshReg$(_c, \"Navbar\");", "map": {"version": 3, "names": ["React", "Link", "useNavigate", "useUser", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON><PERSON>", "_s", "user", "isAuthenticated", "logout", "navigate", "handleLogout", "className", "children", "to", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/user booking/frontend/src/components/Navbar.js"], "sourcesContent": ["import React from 'react';\r\nimport { Link, useNavigate } from 'react-router-dom';\r\nimport { useUser } from '../context/UserContext';\r\nimport './Navbar.css';\r\n\r\nfunction Navbar() {\r\n  const { user, isAuthenticated, logout } = useUser();\r\n  const navigate = useNavigate();\r\n\r\n  const handleLogout = () => {\r\n    logout();\r\n    navigate('/');\r\n  };\r\n\r\n  return (\r\n    <nav className=\"navbar\">\r\n      <div className=\"navbar-brand\">\r\n        <Link to={isAuthenticated ? \"/dashboard\" : \"/\"}>\r\n          <span className=\"brand-name\">AutoCare</span>\r\n        </Link>\r\n      </div>\r\n\r\n      <div className=\"navbar-menu\">\r\n        {isAuthenticated ? (\r\n          <>\r\n            <Link to=\"/dashboard\" className=\"nav-link\">Dashboard</Link>\r\n            <Link to=\"/book\" className=\"nav-link\">Book Service</Link>\r\n            <Link to=\"/bookings\" className=\"nav-link\">My Bookings</Link>\r\n            <div className=\"user-menu\">\r\n              <span className=\"user-name\">Hi, {user?.name}</span>\r\n              <button onClick={handleLogout} className=\"logout-btn\">\r\n                Logout\r\n              </button>\r\n            </div>\r\n          </>\r\n        ) : (\r\n          <>\r\n            <Link to=\"/\" className=\"nav-link\">Home</Link>\r\n            <Link to=\"/login\" className=\"nav-link\">Login</Link>\r\n            <Link to=\"/register\" className=\"nav-link register-link\">Register</Link>\r\n          </>\r\n        )}\r\n      </div>\r\n    </nav>\r\n  );\r\n}\r\n\r\nexport default Navbar;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEtB,SAASC,MAAMA,CAAA,EAAG;EAAAC,EAAA;EAChB,MAAM;IAAEC,IAAI;IAAEC,eAAe;IAAEC;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EACnD,MAAMU,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,MAAMY,YAAY,GAAGA,CAAA,KAAM;IACzBF,MAAM,CAAC,CAAC;IACRC,QAAQ,CAAC,GAAG,CAAC;EACf,CAAC;EAED,oBACER,OAAA;IAAKU,SAAS,EAAC,QAAQ;IAAAC,QAAA,gBACrBX,OAAA;MAAKU,SAAS,EAAC,cAAc;MAAAC,QAAA,eAC3BX,OAAA,CAACJ,IAAI;QAACgB,EAAE,EAAEN,eAAe,GAAG,YAAY,GAAG,GAAI;QAAAK,QAAA,eAC7CX,OAAA;UAAMU,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAENhB,OAAA;MAAKU,SAAS,EAAC,aAAa;MAAAC,QAAA,EACzBL,eAAe,gBACdN,OAAA,CAAAE,SAAA;QAAAS,QAAA,gBACEX,OAAA,CAACJ,IAAI;UAACgB,EAAE,EAAC,YAAY;UAACF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3DhB,OAAA,CAACJ,IAAI;UAACgB,EAAE,EAAC,OAAO;UAACF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACzDhB,OAAA,CAACJ,IAAI;UAACgB,EAAE,EAAC,WAAW;UAACF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5DhB,OAAA;UAAKU,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBX,OAAA;YAAMU,SAAS,EAAC,WAAW;YAAAC,QAAA,GAAC,MAAI,EAACN,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEY,IAAI;UAAA;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACnDhB,OAAA;YAAQkB,OAAO,EAAET,YAAa;YAACC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAEtD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA,eACN,CAAC,gBAEHhB,OAAA,CAAAE,SAAA;QAAAS,QAAA,gBACEX,OAAA,CAACJ,IAAI;UAACgB,EAAE,EAAC,GAAG;UAACF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAI;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7ChB,OAAA,CAACJ,IAAI;UAACgB,EAAE,EAAC,QAAQ;UAACF,SAAS,EAAC,UAAU;UAAAC,QAAA,EAAC;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnDhB,OAAA,CAACJ,IAAI;UAACgB,EAAE,EAAC,WAAW;UAACF,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAC;QAAQ;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,eACvE;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACZ,EAAA,CAxCQD,MAAM;EAAA,QAC6BL,OAAO,EAChCD,WAAW;AAAA;AAAAsB,EAAA,GAFrBhB,MAAM;AA0Cf,eAAeA,MAAM;AAAC,IAAAgB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}