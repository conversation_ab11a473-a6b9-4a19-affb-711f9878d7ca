.navbar {
  background: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 15px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  z-index: 1000;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
  display: flex;
  align-items: center;
}

.navbar-brand a {
  text-decoration: none;
  color: inherit;
}

.brand-name {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 25px;
}

.nav-link {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  font-size: 16px;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-link:hover {
  background: #f8f9fa;
  color: #667eea;
}

.register-link {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white !important;
  font-weight: 600;
}

.register-link:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 15px;
  padding-left: 20px;
  border-left: 1px solid #e1e5e9;
}

.user-name {
  color: #333;
  font-weight: 600;
  font-size: 14px;
}

.logout-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.logout-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* Responsive design */
@media (max-width: 768px) {
  .navbar {
    padding: 15px 20px;
    flex-wrap: wrap;
    gap: 15px;
  }
  
  .navbar-menu {
    gap: 15px;
    flex-wrap: wrap;
  }
  
  .nav-link {
    font-size: 14px;
    padding: 6px 12px;
  }
  
  .user-menu {
    padding-left: 0;
    border-left: none;
    border-top: 1px solid #e1e5e9;
    padding-top: 10px;
    width: 100%;
    justify-content: space-between;
  }
  
  .brand-name {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .navbar {
    padding: 10px 15px;
  }
  
  .navbar-menu {
    gap: 10px;
  }
  
  .nav-link {
    font-size: 13px;
    padding: 5px 10px;
  }
  
  .user-name {
    font-size: 13px;
  }
  
  .logout-btn {
    font-size: 13px;
    padding: 6px 12px;
  }
}
