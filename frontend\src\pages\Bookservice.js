import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { createBooking } from '../api/bookingApi';
import { useUser } from '../context/UserContext';
import './BookService.css';

function BookService() {
  const [formData, setFormData] = useState({
    vehicleType: '',
    serviceType: '',
    date: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const navigate = useNavigate();
  const { user } = useUser();

  const vehicleTypes = [
    'Car', 'Motorcycle', 'Truck', 'SUV', 'Van', 'Bus'
  ];

  const serviceTypes = [
    'Oil Change', 'Brake Service', 'Tire Replacement', 'Engine Repair',
    'Transmission Service', 'Air Conditioning', 'Battery Replacement',
    'General Maintenance', 'Inspection', 'Wheel Alignment'
  ];

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
    // Clear messages when user starts typing
    if (error) setError('');
    if (success) setSuccess('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setSuccess('');

    // Validation
    if (!formData.vehicleType || !formData.serviceType || !formData.date) {
      setError('Please fill in all fields');
      setLoading(false);
      return;
    }

    // Check if date is in the future
    const selectedDate = new Date(formData.date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (selectedDate < today) {
      setError('Please select a future date');
      setLoading(false);
      return;
    }

    try {
      await createBooking(formData.vehicleType, formData.serviceType, formData.date);
      setSuccess('Booking created successfully!');
      setFormData({ vehicleType: '', serviceType: '', date: '' });

      // Redirect to dashboard after 2 seconds
      setTimeout(() => {
        navigate('/dashboard');
      }, 2000);
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to create booking. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="book-service-container">
      <div className="book-service-card">
        <div className="book-service-header">
          <h1>Book a Service</h1>
          <p>Schedule your vehicle service appointment</p>
          <div className="user-info">
            <span>Booking for: <strong>{user?.name}</strong></span>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="book-service-form">
          {error && (
            <div className="error-message">
              <i className="error-icon">⚠️</i>
              {error}
            </div>
          )}

          {success && (
            <div className="success-message">
              <i className="success-icon">✅</i>
              {success}
            </div>
          )}

          <div className="form-group">
            <label htmlFor="vehicleType">Vehicle Type</label>
            <select
              id="vehicleType"
              name="vehicleType"
              value={formData.vehicleType}
              onChange={handleChange}
              required
            >
              <option value="">Select your vehicle type</option>
              {vehicleTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="serviceType">Service Type</label>
            <select
              id="serviceType"
              name="serviceType"
              value={formData.serviceType}
              onChange={handleChange}
              required
            >
              <option value="">Select service type</option>
              {serviceTypes.map(type => (
                <option key={type} value={type}>{type}</option>
              ))}
            </select>
          </div>

          <div className="form-group">
            <label htmlFor="date">Preferred Date</label>
            <input
              type="date"
              id="date"
              name="date"
              value={formData.date}
              onChange={handleChange}
              required
              min={new Date().toISOString().split('T')[0]}
            />
          </div>

          <div className="form-actions">
            <button
              type="button"
              className="btn btn-secondary"
              onClick={() => navigate('/dashboard')}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="btn btn-primary"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span className="spinner"></span>
                  Booking...
                </>
              ) : (
                'Book Service'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}

export default BookService;
