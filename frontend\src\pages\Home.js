import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../context/UserContext';
import './Home.css';

function Home() {
  const navigate = useNavigate();
  const { isAuthenticated, user } = useUser();

  return (
    <div className="home-container">
      <div className="hero-section">
        <h1>Welcome to <span className="brand">AutoCare</span></h1>
        {isAuthenticated ? (
          <div className="authenticated-welcome">
            <p className="tagline">
              Welcome back, <strong>{user?.name}</strong>! Ready to manage your vehicle services?
            </p>
            <div className="cta-buttons">
              <button onClick={() => navigate('/dashboard')}>Go to Dashboard</button>
              <button onClick={() => navigate('/book')}>Book New Service</button>
            </div>
          </div>
        ) : (
          <div className="guest-welcome">
            <p className="tagline">
              A smart and efficient solution for managing vehicle service bookings with ease.
            </p>
            <div className="cta-buttons">
              <button onClick={() => navigate('/login')}>Sign In</button>
              <button onClick={() => navigate('/register')}>Get Started</button>
            </div>
          </div>
        )}
      </div>

      <div className="features-section">
        <h2>Why Choose AutoCare?</h2>
        <ul className="features-list">
          <li>🚗 Easy vehicle service booking in a few clicks</li>
          <li>📅 Schedule service appointments at your convenience</li>
          <li>👥 Seamless user and worker management</li>
          <li>📊 Track your bookings in real-time</li>
          <li>🔒 Secure and responsive user interface</li>
          <li>📱 Modern and intuitive dashboard</li>
        </ul>
      </div>

      {!isAuthenticated && (
        <div className="auth-section">
          <h2>Get Started Today</h2>
          <p>Join thousands of satisfied customers who trust AutoCare for their vehicle service needs.</p>
          <div className="auth-buttons">
            <button
              className="auth-btn login-btn"
              onClick={() => navigate('/login')}
            >
              Already have an account? Sign In
            </button>
            <button
              className="auth-btn register-btn"
              onClick={() => navigate('/register')}
            >
              New to AutoCare? Create Account
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default Home;
