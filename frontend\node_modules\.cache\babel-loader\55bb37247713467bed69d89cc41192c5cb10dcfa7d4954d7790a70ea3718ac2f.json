{"ast": null, "code": "import axios from 'axios';\nconst API = 'http://localhost:2005/api/bookings';\n\n// Set up axios interceptor to include auth token\naxios.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\nexport const fetchBookings = async () => {\n  const res = await axios.get(API);\n  return res.data;\n};\nexport const fetchMyBookings = async () => {\n  const res = await axios.get(`${API}/my-bookings`);\n  return res.data;\n};\nexport const createBooking = async (vehicleType, serviceType, date) => {\n  const res = await axios.post(API, {\n    vehicleType,\n    serviceType,\n    date\n  });\n  return res.data;\n};\nexport const updateBooking = async (bookingId, status) => {\n  const res = await axios.put(`${API}/${bookingId}`, {\n    status\n  });\n  return res.data;\n};\nexport const deleteBooking = async bookingId => {\n  const res = await axios.delete(`${API}/${bookingId}`);\n  return res.data;\n};", "map": {"version": 3, "names": ["axios", "API", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "fetchBookings", "res", "get", "data", "fetchMyBookings", "createBooking", "vehicleType", "serviceType", "date", "post", "updateBooking", "bookingId", "status", "put", "deleteBooking", "delete"], "sources": ["D:/user booking/frontend/src/api/bookingApi.js"], "sourcesContent": ["import axios from 'axios';\r\nconst API = 'http://localhost:2005/api/bookings';\r\n\r\n// Set up axios interceptor to include auth token\r\naxios.interceptors.request.use((config) => {\r\n  const token = localStorage.getItem('token');\r\n  if (token) {\r\n    config.headers.Authorization = `Bearer ${token}`;\r\n  }\r\n  return config;\r\n});\r\n\r\nexport const fetchBookings = async () => {\r\n  const res = await axios.get(API);\r\n  return res.data;\r\n};\r\n\r\nexport const fetchMyBookings = async () => {\r\n  const res = await axios.get(`${API}/my-bookings`);\r\n  return res.data;\r\n};\r\n\r\nexport const createBooking = async (vehicleType, serviceType, date) => {\r\n  const res = await axios.post(API, { vehicleType, serviceType, date });\r\n  return res.data;\r\n};\r\n\r\nexport const updateBooking = async (bookingId, status) => {\r\n  const res = await axios.put(`${API}/${bookingId}`, { status });\r\n  return res.data;\r\n};\r\n\r\nexport const deleteBooking = async (bookingId) => {\r\n  const res = await axios.delete(`${API}/${bookingId}`);\r\n  return res.data;\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,GAAG,GAAG,oCAAoC;;AAEhD;AACAD,KAAK,CAACE,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACzC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;AAEF,OAAO,MAAMM,aAAa,GAAG,MAAAA,CAAA,KAAY;EACvC,MAAMC,GAAG,GAAG,MAAMZ,KAAK,CAACa,GAAG,CAACZ,GAAG,CAAC;EAChC,OAAOW,GAAG,CAACE,IAAI;AACjB,CAAC;AAED,OAAO,MAAMC,eAAe,GAAG,MAAAA,CAAA,KAAY;EACzC,MAAMH,GAAG,GAAG,MAAMZ,KAAK,CAACa,GAAG,CAAC,GAAGZ,GAAG,cAAc,CAAC;EACjD,OAAOW,GAAG,CAACE,IAAI;AACjB,CAAC;AAED,OAAO,MAAME,aAAa,GAAG,MAAAA,CAAOC,WAAW,EAAEC,WAAW,EAAEC,IAAI,KAAK;EACrE,MAAMP,GAAG,GAAG,MAAMZ,KAAK,CAACoB,IAAI,CAACnB,GAAG,EAAE;IAAEgB,WAAW;IAAEC,WAAW;IAAEC;EAAK,CAAC,CAAC;EACrE,OAAOP,GAAG,CAACE,IAAI;AACjB,CAAC;AAED,OAAO,MAAMO,aAAa,GAAG,MAAAA,CAAOC,SAAS,EAAEC,MAAM,KAAK;EACxD,MAAMX,GAAG,GAAG,MAAMZ,KAAK,CAACwB,GAAG,CAAC,GAAGvB,GAAG,IAAIqB,SAAS,EAAE,EAAE;IAAEC;EAAO,CAAC,CAAC;EAC9D,OAAOX,GAAG,CAACE,IAAI;AACjB,CAAC;AAED,OAAO,MAAMW,aAAa,GAAG,MAAOH,SAAS,IAAK;EAChD,MAAMV,GAAG,GAAG,MAAMZ,KAAK,CAAC0B,MAAM,CAAC,GAAGzB,GAAG,IAAIqB,SAAS,EAAE,CAAC;EACrD,OAAOV,GAAG,CAACE,IAAI;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}