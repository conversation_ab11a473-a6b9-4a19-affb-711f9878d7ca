{"ast": null, "code": "var _jsxFileName = \"D:\\\\user booking\\\\frontend\\\\src\\\\App.js\";\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { UserProvider } from './context/UserContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Home from './pages/Home';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport Dashboard from './pages/Dashboard';\nimport BookService from './pages/Bookservice';\nimport UsersList from './pages/UsersList';\nimport BookingsList from './pages/BookingList';\nimport Navbar from './components/Navbar';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(UserProvider, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: /*#__PURE__*/_jsxDEV(Routes, {\n        children: [/*#__PURE__*/_jsxDEV(Route, {\n          path: \"/\",\n          element: /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 38\n            }, this), /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 48\n            }, this)]\n          }, void 0, true)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/login\",\n          element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 41\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/register\",\n          element: /*#__PURE__*/_jsxDEV(Register, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 44\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/dashboard\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/book\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: /*#__PURE__*/_jsxDEV(BookService, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/users\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(UsersList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"/bookings\",\n          element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n            children: [/*#__PURE__*/_jsxDEV(Navbar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(BookingsList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "UserProvider", "ProtectedRoute", "Home", "<PERSON><PERSON>", "Register", "Dashboard", "BookService", "UsersList", "BookingsList", "<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "App", "children", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["D:/user booking/frontend/src/App.js"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { UserProvider } from './context/UserContext';\nimport ProtectedRoute from './components/ProtectedRoute';\nimport Home from './pages/Home';\nimport Login from './pages/Login';\nimport Register from './pages/Register';\nimport Dashboard from './pages/Dashboard';\nimport BookService from './pages/Bookservice';\nimport UsersList from './pages/UsersList';\nimport BookingsList from './pages/BookingList';\nimport Navbar from './components/Navbar';\n\nfunction App() {\n  return (\n    <UserProvider>\n      <div className=\"App\">\n        <Routes>\n          {/* Public routes */}\n          <Route path=\"/\" element={<><Navbar /><Home /></>} />\n          <Route path=\"/login\" element={<Login />} />\n          <Route path=\"/register\" element={<Register />} />\n\n          {/* Protected routes */}\n          <Route path=\"/dashboard\" element={\n            <ProtectedRoute>\n              <Navbar />\n              <Dashboard />\n            </ProtectedRoute>\n          } />\n\n          <Route path=\"/book\" element={\n            <ProtectedRoute>\n              <BookService />\n            </ProtectedRoute>\n          } />\n\n          <Route path=\"/users\" element={\n            <ProtectedRoute>\n              <Navbar />\n              <UsersList />\n            </ProtectedRoute>\n          } />\n\n          <Route path=\"/bookings\" element={\n            <ProtectedRoute>\n              <Navbar />\n              <BookingsList />\n            </ProtectedRoute>\n          } />\n\n          {/* Redirect any unknown routes to home */}\n          <Route path=\"*\" element={<Navigate to=\"/\" replace />} />\n        </Routes>\n      </div>\n    </UserProvider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,YAAY,QAAQ,uBAAuB;AACpD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,SAAS,MAAM,mBAAmB;AACzC,OAAOC,YAAY,MAAM,qBAAqB;AAC9C,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEzC,SAASC,GAAGA,CAAA,EAAG;EACb,oBACEH,OAAA,CAACX,YAAY;IAAAe,QAAA,eACXJ,OAAA;MAAKK,SAAS,EAAC,KAAK;MAAAD,QAAA,eAClBJ,OAAA,CAACd,MAAM;QAAAkB,QAAA,gBAELJ,OAAA,CAACb,KAAK;UAACmB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEP,OAAA,CAAAE,SAAA;YAAAE,QAAA,gBAAEJ,OAAA,CAACF,MAAM;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAAAX,OAAA,CAACT,IAAI;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA,eAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACpDX,OAAA,CAACb,KAAK;UAACmB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAAEP,OAAA,CAACR,KAAK;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC3CX,OAAA,CAACb,KAAK;UAACmB,IAAI,EAAC,WAAW;UAACC,OAAO,eAAEP,OAAA,CAACP,QAAQ;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGjDX,OAAA,CAACb,KAAK;UAACmB,IAAI,EAAC,YAAY;UAACC,OAAO,eAC9BP,OAAA,CAACV,cAAc;YAAAc,QAAA,gBACbJ,OAAA,CAACF,MAAM;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACVX,OAAA,CAACN,SAAS;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJX,OAAA,CAACb,KAAK;UAACmB,IAAI,EAAC,OAAO;UAACC,OAAO,eACzBP,OAAA,CAACV,cAAc;YAAAc,QAAA,eACbJ,OAAA,CAACL,WAAW;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJX,OAAA,CAACb,KAAK;UAACmB,IAAI,EAAC,QAAQ;UAACC,OAAO,eAC1BP,OAAA,CAACV,cAAc;YAAAc,QAAA,gBACbJ,OAAA,CAACF,MAAM;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACVX,OAAA,CAACJ,SAAS;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAEJX,OAAA,CAACb,KAAK;UAACmB,IAAI,EAAC,WAAW;UAACC,OAAO,eAC7BP,OAAA,CAACV,cAAc;YAAAc,QAAA,gBACbJ,OAAA,CAACF,MAAM;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACVX,OAAA,CAACH,YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QACjB;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGJX,OAAA,CAACb,KAAK;UAACmB,IAAI,EAAC,GAAG;UAACC,OAAO,eAAEP,OAAA,CAACZ,QAAQ;YAACwB,EAAE,EAAC,GAAG;YAACC,OAAO;UAAA;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACM,CAAC;AAEnB;AAACG,EAAA,GA5CQX,GAAG;AA8CZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}