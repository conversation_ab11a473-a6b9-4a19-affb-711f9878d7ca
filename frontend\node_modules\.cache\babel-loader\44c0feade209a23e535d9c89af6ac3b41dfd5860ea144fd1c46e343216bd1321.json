{"ast": null, "code": "var _jsxFileName = \"D:\\\\user booking\\\\frontend\\\\src\\\\pages\\\\Home.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { useUser } from '../context/UserContext';\nimport './Home.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction Home() {\n  _s();\n  const navigate = useNavigate();\n  const {\n    isAuthenticated,\n    user\n  } = useUser();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"home-container\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: [\"Welcome to \", /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"brand\",\n          children: \"AutoCare\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 24\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), isAuthenticated ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"authenticated-welcome\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"tagline\",\n          children: [\"Welcome back, \", /*#__PURE__*/_jsxDEV(\"strong\", {\n            children: user === null || user === void 0 ? void 0 : user.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 29\n          }, this), \"! Ready to manage your vehicle services?\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cta-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/dashboard'),\n            children: \"Go to Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/book'),\n            children: \"Book New Service\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 19,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"guest-welcome\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"tagline\",\n          children: \"A smart and efficient solution for managing vehicle service bookings with ease.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"cta-buttons\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/login'),\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => navigate('/register'),\n            children: \"Get Started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"features-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Why Choose AutoCare?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"features-list\",\n        children: [/*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\uD83D\\uDE97 Easy vehicle service booking in a few clicks\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\uD83D\\uDCC5 Schedule service appointments at your convenience\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\uD83D\\uDC65 Seamless user and worker management\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\uD83D\\uDCCA Track your bookings in real-time\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\uD83D\\uDD12 Secure and responsive user interface\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n          children: \"\\uD83D\\uDCF1 Modern and intuitive dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 39,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 37,\n      columnNumber: 7\n    }, this), !isAuthenticated && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"auth-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Get Started Today\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Join thousands of satisfied customers who trust AutoCare for their vehicle service needs.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"auth-buttons\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"auth-btn login-btn\",\n          onClick: () => navigate('/login'),\n          children: \"Already have an account? Sign In\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"auth-btn register-btn\",\n          onClick: () => navigate('/register'),\n          children: \"New to AutoCare? Create Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n}\n_s(Home, \"N1Qwpt+Zwr0vWbW4S3JLbqardOk=\", false, function () {\n  return [useNavigate, useUser];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["React", "useNavigate", "useUser", "jsxDEV", "_jsxDEV", "Home", "_s", "navigate", "isAuthenticated", "user", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/user booking/frontend/src/pages/Home.js"], "sourcesContent": ["import React from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport { useUser } from '../context/UserContext';\r\nimport './Home.css';\r\n\r\nfunction Home() {\r\n  const navigate = useNavigate();\r\n  const { isAuthenticated, user } = useUser();\r\n\r\n  return (\r\n    <div className=\"home-container\">\r\n      <div className=\"hero-section\">\r\n        <h1>Welcome to <span className=\"brand\">AutoCare</span></h1>\r\n        {isAuthenticated ? (\r\n          <div className=\"authenticated-welcome\">\r\n            <p className=\"tagline\">\r\n              Welcome back, <strong>{user?.name}</strong>! Ready to manage your vehicle services?\r\n            </p>\r\n            <div className=\"cta-buttons\">\r\n              <button onClick={() => navigate('/dashboard')}>Go to Dashboard</button>\r\n              <button onClick={() => navigate('/book')}>Book New Service</button>\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <div className=\"guest-welcome\">\r\n            <p className=\"tagline\">\r\n              A smart and efficient solution for managing vehicle service bookings with ease.\r\n            </p>\r\n            <div className=\"cta-buttons\">\r\n              <button onClick={() => navigate('/login')}>Sign In</button>\r\n              <button onClick={() => navigate('/register')}>Get Started</button>\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className=\"features-section\">\r\n        <h2>Why Choose AutoCare?</h2>\r\n        <ul className=\"features-list\">\r\n          <li>🚗 Easy vehicle service booking in a few clicks</li>\r\n          <li>📅 Schedule service appointments at your convenience</li>\r\n          <li>👥 Seamless user and worker management</li>\r\n          <li>📊 Track your bookings in real-time</li>\r\n          <li>🔒 Secure and responsive user interface</li>\r\n          <li>📱 Modern and intuitive dashboard</li>\r\n        </ul>\r\n      </div>\r\n\r\n      {!isAuthenticated && (\r\n        <div className=\"auth-section\">\r\n          <h2>Get Started Today</h2>\r\n          <p>Join thousands of satisfied customers who trust AutoCare for their vehicle service needs.</p>\r\n          <div className=\"auth-buttons\">\r\n            <button\r\n              className=\"auth-btn login-btn\"\r\n              onClick={() => navigate('/login')}\r\n            >\r\n              Already have an account? Sign In\r\n            </button>\r\n            <button\r\n              className=\"auth-btn register-btn\"\r\n              onClick={() => navigate('/register')}\r\n            >\r\n              New to AutoCare? Create Account\r\n            </button>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,wBAAwB;AAChD,OAAO,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpB,SAASC,IAAIA,CAAA,EAAG;EAAAC,EAAA;EACd,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEO,eAAe;IAAEC;EAAK,CAAC,GAAGP,OAAO,CAAC,CAAC;EAE3C,oBACEE,OAAA;IAAKM,SAAS,EAAC,gBAAgB;IAAAC,QAAA,gBAC7BP,OAAA;MAAKM,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BP,OAAA;QAAAO,QAAA,GAAI,aAAW,eAAAP,OAAA;UAAMM,SAAS,EAAC,OAAO;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC1DP,eAAe,gBACdJ,OAAA;QAAKM,SAAS,EAAC,uBAAuB;QAAAC,QAAA,gBACpCP,OAAA;UAAGM,SAAS,EAAC,SAAS;UAAAC,QAAA,GAAC,gBACP,eAAAP,OAAA;YAAAO,QAAA,EAASF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEO;UAAI;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAS,CAAC,4CAC7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJX,OAAA;UAAKM,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BP,OAAA;YAAQa,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,YAAY,CAAE;YAAAI,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACvEX,OAAA;YAAQa,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,OAAO,CAAE;YAAAI,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,gBAENX,OAAA;QAAKM,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BP,OAAA;UAAGM,SAAS,EAAC,SAAS;UAAAC,QAAA,EAAC;QAEvB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJX,OAAA;UAAKM,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BP,OAAA;YAAQa,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,QAAQ,CAAE;YAAAI,QAAA,EAAC;UAAO;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3DX,OAAA;YAAQa,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,WAAW,CAAE;YAAAI,QAAA,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAENX,OAAA;MAAKM,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BP,OAAA;QAAAO,QAAA,EAAI;MAAoB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BX,OAAA;QAAIM,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC3BP,OAAA;UAAAO,QAAA,EAAI;QAA+C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACxDX,OAAA;UAAAO,QAAA,EAAI;QAAoD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7DX,OAAA;UAAAO,QAAA,EAAI;QAAsC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC/CX,OAAA;UAAAO,QAAA,EAAI;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5CX,OAAA;UAAAO,QAAA,EAAI;QAAuC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChDX,OAAA;UAAAO,QAAA,EAAI;QAAiC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEL,CAACP,eAAe,iBACfJ,OAAA;MAAKM,SAAS,EAAC,cAAc;MAAAC,QAAA,gBAC3BP,OAAA;QAAAO,QAAA,EAAI;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1BX,OAAA;QAAAO,QAAA,EAAG;MAAyF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eAChGX,OAAA;QAAKM,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BP,OAAA;UACEM,SAAS,EAAC,oBAAoB;UAC9BO,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,QAAQ,CAAE;UAAAI,QAAA,EACnC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTX,OAAA;UACEM,SAAS,EAAC,uBAAuB;UACjCO,OAAO,EAAEA,CAAA,KAAMV,QAAQ,CAAC,WAAW,CAAE;UAAAI,QAAA,EACtC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACT,EAAA,CAjEQD,IAAI;EAAA,QACMJ,WAAW,EACMC,OAAO;AAAA;AAAAgB,EAAA,GAFlCb,IAAI;AAmEb,eAAeA,IAAI;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}