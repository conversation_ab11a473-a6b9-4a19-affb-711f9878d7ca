{"ast": null, "code": "import axios from 'axios';\nconst API = 'http://localhost:2005/api/users';\n\n// Set up axios interceptor to include auth token\naxios.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n});\nexport const fetchUsers = async () => {\n  const res = await axios.get(API);\n  return res.data;\n};\nexport const registerUser = async (name, email, password) => {\n  const res = await axios.post(`${API}/register`, {\n    name,\n    email,\n    password\n  });\n  return res.data;\n};\nexport const loginUser = async (email, password) => {\n  const res = await axios.post(`${API}/login`, {\n    email,\n    password\n  });\n  return res.data;\n};\nexport const getUserProfile = async () => {\n  const res = await axios.get(`${API}/profile`);\n  return res.data;\n};", "map": {"version": 3, "names": ["axios", "API", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "headers", "Authorization", "fetchUsers", "res", "get", "data", "registerUser", "name", "email", "password", "post", "loginUser", "getUserProfile"], "sources": ["D:/user booking/frontend/src/api/userApi.js"], "sourcesContent": ["import axios from 'axios';\r\nconst API = 'http://localhost:2005/api/users';\r\n\r\n// Set up axios interceptor to include auth token\r\naxios.interceptors.request.use((config) => {\r\n  const token = localStorage.getItem('token');\r\n  if (token) {\r\n    config.headers.Authorization = `Bearer ${token}`;\r\n  }\r\n  return config;\r\n});\r\n\r\nexport const fetchUsers = async () => {\r\n  const res = await axios.get(API);\r\n  return res.data;\r\n};\r\n\r\nexport const registerUser = async (name, email, password) => {\r\n  const res = await axios.post(`${API}/register`, { name, email, password });\r\n  return res.data;\r\n};\r\n\r\nexport const loginUser = async (email, password) => {\r\n  const res = await axios.post(`${API}/login`, { email, password });\r\n  return res.data;\r\n};\r\n\r\nexport const getUserProfile = async () => {\r\n  const res = await axios.get(`${API}/profile`);\r\n  return res.data;\r\n};\r\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,MAAMC,GAAG,GAAG,iCAAiC;;AAE7C;AACAD,KAAK,CAACE,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACzC,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACI,OAAO,CAACC,aAAa,GAAG,UAAUJ,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,CAAC;AAEF,OAAO,MAAMM,UAAU,GAAG,MAAAA,CAAA,KAAY;EACpC,MAAMC,GAAG,GAAG,MAAMZ,KAAK,CAACa,GAAG,CAACZ,GAAG,CAAC;EAChC,OAAOW,GAAG,CAACE,IAAI;AACjB,CAAC;AAED,OAAO,MAAMC,YAAY,GAAG,MAAAA,CAAOC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,KAAK;EAC3D,MAAMN,GAAG,GAAG,MAAMZ,KAAK,CAACmB,IAAI,CAAC,GAAGlB,GAAG,WAAW,EAAE;IAAEe,IAAI;IAAEC,KAAK;IAAEC;EAAS,CAAC,CAAC;EAC1E,OAAON,GAAG,CAACE,IAAI;AACjB,CAAC;AAED,OAAO,MAAMM,SAAS,GAAG,MAAAA,CAAOH,KAAK,EAAEC,QAAQ,KAAK;EAClD,MAAMN,GAAG,GAAG,MAAMZ,KAAK,CAACmB,IAAI,CAAC,GAAGlB,GAAG,QAAQ,EAAE;IAAEgB,KAAK;IAAEC;EAAS,CAAC,CAAC;EACjE,OAAON,GAAG,CAACE,IAAI;AACjB,CAAC;AAED,OAAO,MAAMO,cAAc,GAAG,MAAAA,CAAA,KAAY;EACxC,MAAMT,GAAG,GAAG,MAAMZ,KAAK,CAACa,GAAG,CAAC,GAAGZ,GAAG,UAAU,CAAC;EAC7C,OAAOW,GAAG,CAACE,IAAI;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}