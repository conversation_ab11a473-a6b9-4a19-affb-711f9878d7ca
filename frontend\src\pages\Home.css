.home-container {
  padding: 40px;
  max-width: 1000px;
  margin: auto;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
}

.hero-section {
  text-align: center;
  margin-bottom: 40px;
}

.hero-section h1 {
  font-size: 2.8rem;
  color: #2c3e50;
}

.hero-section .brand {
  color: #1abc9c;
}

.tagline {
  font-size: 1.2rem;
  margin-top: 10px;
  color: #555;
}

.cta-buttons {
  margin-top: 30px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.cta-buttons button {
  padding: 15px 30px;
  font-size: 16px;
  font-weight: bold;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.cta-buttons button:first-child {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.cta-buttons button:first-child:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.cta-buttons button:last-child {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
}

.cta-buttons button:last-child:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

.features-section {
  margin-top: 40px;
}

.features-section h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
}

.features-list {
  list-style: none;
  padding: 0;
  font-size: 1.1rem;
  color: #444;
  line-height: 2rem;
}

.features-list li::before {
  content: '✔️';
  margin-right: 10px;
  color: #1abc9c;
}

.authenticated-welcome,
.guest-welcome {
  text-align: center;
}

.authenticated-welcome .tagline strong {
  color: #667eea;
}

.auth-section {
  background: white;
  padding: 60px 40px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  margin-top: 60px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.auth-section h2 {
  color: #333;
  font-size: 28px;
  margin-bottom: 15px;
  font-weight: 700;
}

.auth-section p {
  color: #666;
  font-size: 18px;
  margin-bottom: 30px;
  line-height: 1.6;
}

.auth-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-width: 400px;
  margin: 0 auto;
}

.auth-btn {
  padding: 15px 25px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.login-btn {
  background: #f8f9fa;
  color: #667eea;
  border: 2px solid #667eea;
}

.login-btn:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
}

.register-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.register-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Responsive design */
@media (max-width: 768px) {
  .cta-buttons {
    flex-direction: column;
    align-items: center;
  }

  .auth-buttons {
    max-width: 100%;
  }

  .auth-section {
    padding: 40px 20px;
    margin-top: 40px;
  }
}
