{"ast": null, "code": "var _jsxFileName = \"D:\\\\user booking\\\\frontend\\\\src\\\\pages\\\\Login.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { loginUser } from '../api/userApi';\nimport './Login.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nfunction Login() {\n  _s();\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n    try {\n      const response = await loginUser(formData.email, formData.password);\n\n      // Store token and user data\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n\n      // Redirect to dashboard\n      navigate('/dashboard');\n    } catch (err) {\n      var _err$response, _err$response$data;\n      setError(((_err$response = err.response) === null || _err$response === void 0 ? void 0 : (_err$response$data = _err$response.data) === null || _err$response$data === void 0 ? void 0 : _err$response$data.error) || 'Login failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          children: \"Welcome Back\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Sign in to your AutoCare account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"login-form\",\n        children: [error && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"error-message\",\n          children: [/*#__PURE__*/_jsxDEV(\"i\", {\n            className: \"error-icon\",\n            children: \"\\u26A0\\uFE0F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 15\n          }, this), error]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"email\",\n            children: \"Email Address\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"email\",\n            id: \"email\",\n            name: \"email\",\n            value: formData.email,\n            onChange: handleChange,\n            required: true,\n            placeholder: \"Enter your email\",\n            className: error ? 'error' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"form-group\",\n          children: [/*#__PURE__*/_jsxDEV(\"label\", {\n            htmlFor: \"password\",\n            children: \"Password\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"password\",\n            id: \"password\",\n            name: \"password\",\n            value: formData.password,\n            onChange: handleChange,\n            required: true,\n            placeholder: \"Enter your password\",\n            className: error ? 'error' : ''\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: \"login-btn\",\n          disabled: loading,\n          children: loading ? /*#__PURE__*/_jsxDEV(_Fragment, {\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"spinner\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 17\n            }, this), \"Signing in...\"]\n          }, void 0, true) : 'Sign In'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: [\"Don't have an account?\", ' ', /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"button\",\n            className: \"link-btn\",\n            onClick: () => navigate('/register'),\n            children: \"Sign up here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n}\n_s(Login, \"pcHCE7A8Qj3b+7djZ/6tNZpaj4Q=\", false, function () {\n  return [useNavigate];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "loginUser", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "formData", "setFormData", "email", "password", "loading", "setLoading", "error", "setError", "navigate", "handleChange", "e", "target", "name", "value", "handleSubmit", "preventDefault", "response", "localStorage", "setItem", "token", "JSON", "stringify", "user", "err", "_err$response", "_err$response$data", "data", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "id", "onChange", "required", "placeholder", "disabled", "onClick", "_c", "$RefreshReg$"], "sources": ["D:/user booking/frontend/src/pages/Login.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { loginUser } from '../api/userApi';\nimport './Login.css';\n\nfunction Login() {\n  const [formData, setFormData] = useState({\n    email: '',\n    password: ''\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const navigate = useNavigate();\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n    // Clear error when user starts typing\n    if (error) setError('');\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const response = await loginUser(formData.email, formData.password);\n      \n      // Store token and user data\n      localStorage.setItem('token', response.token);\n      localStorage.setItem('user', JSON.stringify(response.user));\n      \n      // Redirect to dashboard\n      navigate('/dashboard');\n    } catch (err) {\n      setError(err.response?.data?.error || 'Login failed. Please try again.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"login-container\">\n      <div className=\"login-card\">\n        <div className=\"login-header\">\n          <h1>Welcome Back</h1>\n          <p>Sign in to your AutoCare account</p>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"login-form\">\n          {error && (\n            <div className=\"error-message\">\n              <i className=\"error-icon\">⚠️</i>\n              {error}\n            </div>\n          )}\n\n          <div className=\"form-group\">\n            <label htmlFor=\"email\">Email Address</label>\n            <input\n              type=\"email\"\n              id=\"email\"\n              name=\"email\"\n              value={formData.email}\n              onChange={handleChange}\n              required\n              placeholder=\"Enter your email\"\n              className={error ? 'error' : ''}\n            />\n          </div>\n\n          <div className=\"form-group\">\n            <label htmlFor=\"password\">Password</label>\n            <input\n              type=\"password\"\n              id=\"password\"\n              name=\"password\"\n              value={formData.password}\n              onChange={handleChange}\n              required\n              placeholder=\"Enter your password\"\n              className={error ? 'error' : ''}\n            />\n          </div>\n\n          <button \n            type=\"submit\" \n            className=\"login-btn\"\n            disabled={loading}\n          >\n            {loading ? (\n              <>\n                <span className=\"spinner\"></span>\n                Signing in...\n              </>\n            ) : (\n              'Sign In'\n            )}\n          </button>\n        </form>\n\n        <div className=\"login-footer\">\n          <p>\n            Don't have an account?{' '}\n            <button \n              type=\"button\"\n              className=\"link-btn\"\n              onClick={() => navigate('/register')}\n            >\n              Sign up here\n            </button>\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,SAASC,KAAKA,CAAA,EAAG;EAAAC,EAAA;EACf,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IACvCW,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMiB,QAAQ,GAAGhB,WAAW,CAAC,CAAC;EAE9B,MAAMiB,YAAY,GAAIC,CAAC,IAAK;IAC1BT,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACU,CAAC,CAACC,MAAM,CAACC,IAAI,GAAGF,CAAC,CAACC,MAAM,CAACE;IAC5B,CAAC,CAAC;IACF;IACA,IAAIP,KAAK,EAAEC,QAAQ,CAAC,EAAE,CAAC;EACzB,CAAC;EAED,MAAMO,YAAY,GAAG,MAAOJ,CAAC,IAAK;IAChCA,CAAC,CAACK,cAAc,CAAC,CAAC;IAClBV,UAAU,CAAC,IAAI,CAAC;IAChBE,QAAQ,CAAC,EAAE,CAAC;IAEZ,IAAI;MACF,MAAMS,QAAQ,GAAG,MAAMvB,SAAS,CAACO,QAAQ,CAACE,KAAK,EAAEF,QAAQ,CAACG,QAAQ,CAAC;;MAEnE;MACAc,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAACG,KAAK,CAAC;MAC7CF,YAAY,CAACC,OAAO,CAAC,MAAM,EAAEE,IAAI,CAACC,SAAS,CAACL,QAAQ,CAACM,IAAI,CAAC,CAAC;;MAE3D;MACAd,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOe,GAAG,EAAE;MAAA,IAAAC,aAAA,EAAAC,kBAAA;MACZlB,QAAQ,CAAC,EAAAiB,aAAA,GAAAD,GAAG,CAACP,QAAQ,cAAAQ,aAAA,wBAAAC,kBAAA,GAAZD,aAAA,CAAcE,IAAI,cAAAD,kBAAA,uBAAlBA,kBAAA,CAAoBnB,KAAK,KAAI,iCAAiC,CAAC;IAC1E,CAAC,SAAS;MACRD,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEV,OAAA;IAAKgC,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BjC,OAAA;MAAKgC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBACzBjC,OAAA;QAAKgC,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BjC,OAAA;UAAAiC,QAAA,EAAI;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrBrC,OAAA;UAAAiC,QAAA,EAAG;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC,CAAC,eAENrC,OAAA;QAAMsC,QAAQ,EAAEnB,YAAa;QAACa,SAAS,EAAC,YAAY;QAAAC,QAAA,GACjDtB,KAAK,iBACJX,OAAA;UAAKgC,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BjC,OAAA;YAAGgC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,EAC/B1B,KAAK;QAAA;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDrC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBjC,OAAA;YAAOuC,OAAO,EAAC,OAAO;YAAAN,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC5CrC,OAAA;YACEwC,IAAI,EAAC,OAAO;YACZC,EAAE,EAAC,OAAO;YACVxB,IAAI,EAAC,OAAO;YACZC,KAAK,EAAEb,QAAQ,CAACE,KAAM;YACtBmC,QAAQ,EAAE5B,YAAa;YACvB6B,QAAQ;YACRC,WAAW,EAAC,kBAAkB;YAC9BZ,SAAS,EAAErB,KAAK,GAAG,OAAO,GAAG;UAAG;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENrC,OAAA;UAAKgC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBACzBjC,OAAA;YAAOuC,OAAO,EAAC,UAAU;YAAAN,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC1CrC,OAAA;YACEwC,IAAI,EAAC,UAAU;YACfC,EAAE,EAAC,UAAU;YACbxB,IAAI,EAAC,UAAU;YACfC,KAAK,EAAEb,QAAQ,CAACG,QAAS;YACzBkC,QAAQ,EAAE5B,YAAa;YACvB6B,QAAQ;YACRC,WAAW,EAAC,qBAAqB;YACjCZ,SAAS,EAAErB,KAAK,GAAG,OAAO,GAAG;UAAG;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENrC,OAAA;UACEwC,IAAI,EAAC,QAAQ;UACbR,SAAS,EAAC,WAAW;UACrBa,QAAQ,EAAEpC,OAAQ;UAAAwB,QAAA,EAEjBxB,OAAO,gBACNT,OAAA,CAAAE,SAAA;YAAA+B,QAAA,gBACEjC,OAAA;cAAMgC,SAAS,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,iBAEnC;UAAA,eAAE,CAAC,GAEH;QACD;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC,eAEPrC,OAAA;QAAKgC,SAAS,EAAC,cAAc;QAAAC,QAAA,eAC3BjC,OAAA;UAAAiC,QAAA,GAAG,wBACqB,EAAC,GAAG,eAC1BjC,OAAA;YACEwC,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,UAAU;YACpBc,OAAO,EAAEA,CAAA,KAAMjC,QAAQ,CAAC,WAAW,CAAE;YAAAoB,QAAA,EACtC;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACjC,EAAA,CAlHQD,KAAK;EAAA,QAOKN,WAAW;AAAA;AAAAkD,EAAA,GAPrB5C,KAAK;AAoHd,eAAeA,KAAK;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}