const express = require('express');
const router = express.Router();
const Booking = require('../models/Booking');
const auth = require('../middleware/auth');

// Create a new booking (authenticated users only)
router.post('/', auth, async (req, res) => {
  try {
    const { vehicleType, serviceType, date } = req.body;

    // Validate input
    if (!vehicleType || !serviceType || !date) {
      return res.status(400).json({ error: 'Please provide all required fields' });
    }

    // Use authenticated user's ID
    const booking = new Booking({
      userId: req.user._id,
      vehicleType,
      serviceType,
      date
    });

    await booking.save();

    // Populate user info for response
    await booking.populate('userId', 'name email');

    res.status(201).json({
      message: 'Booking created successfully',
      booking
    });
  } catch (err) {
    console.error('Booking creation error:', err);
    res.status(400).json({ error: 'Failed to create booking' });
  }
});

// Get user's own bookings
router.get('/my-bookings', auth, async (req, res) => {
  try {
    const bookings = await Booking.find({ userId: req.user._id })
      .populate('userId', 'name email')
      .sort({ createdAt: -1 });
    res.json(bookings);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch your bookings' });
  }
});

// Get all bookings (admin view - for now just protected)
router.get('/', auth, async (req, res) => {
  try {
    const bookings = await Booking.find().populate('userId', 'name email').sort({ createdAt: -1 });
    res.json(bookings);
  } catch (err) {
    res.status(500).json({ error: 'Failed to fetch bookings' });
  }
});

// Update booking status (user can only update their own bookings)
router.put('/:id', auth, async (req, res) => {
  try {
    const { status } = req.body;

    // Find booking and check ownership
    const booking = await Booking.findById(req.params.id);
    if (!booking) {
      return res.status(404).json({ error: 'Booking not found' });
    }

    if (booking.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to update this booking' });
    }

    const updated = await Booking.findByIdAndUpdate(
      req.params.id,
      { status },
      { new: true }
    ).populate('userId', 'name email');

    res.json({ message: 'Booking updated successfully', booking: updated });
  } catch (err) {
    res.status(400).json({ error: 'Failed to update booking' });
  }
});

// Delete a booking (user can only delete their own bookings)
router.delete('/:id', auth, async (req, res) => {
  try {
    // Find booking and check ownership
    const booking = await Booking.findById(req.params.id);
    if (!booking) {
      return res.status(404).json({ error: 'Booking not found' });
    }

    if (booking.userId.toString() !== req.user._id.toString()) {
      return res.status(403).json({ error: 'Not authorized to delete this booking' });
    }

    await Booking.findByIdAndDelete(req.params.id);
    res.json({ message: 'Booking deleted successfully' });
  } catch (err) {
    res.status(500).json({ error: 'Failed to delete booking' });
  }
});

module.exports = router;
